<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Calculation\FrequencyService;
use App\Services\Calculation\CalculationService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class FrequencyServiceTest extends TestCase
{
    protected FrequencyService $frequencyService;

    protected function setUp(): void
    {
        parent::setUp();

        $calculationService = new CalculationService();
        $this->frequencyService = new FrequencyService($calculationService);
    }

    public function test_calculate_frequency_returns_valid_result(): void
    {
        $frequency = $this->frequencyService->calculateFrequency('test topic');

        $this->assertIsInt($frequency);
        $this->assertGreaterThanOrEqual(0, $frequency);
    }

    public function test_calculate_frequency_safely_with_fallback(): void
    {
        $frequency = $this->frequencyService->calculateFrequencySafely('', [
            'fallback_frequency' => 250
        ]);

        $this->assertEquals(250, $frequency);
    }

    public function test_validate_frequency_range(): void
    {
        $this->assertTrue($this->frequencyService->validateFrequency(440));
        $this->assertFalse($this->frequencyService->validateFrequency(100));
        $this->assertFalse($this->frequencyService->validateFrequency(25000));
    }

    public function test_transform_frequency_applies_business_rules(): void
    {
        // Test frequency below 120 gets multiplied by 3
        $result = $this->frequencyService->transformFrequency(100);
        $this->assertEquals(300, $result);

        // Test frequency between 120-240 gets multiplied by 2
        $result = $this->frequencyService->transformFrequency(150);
        $this->assertEquals(300, $result);

        // Test frequency between 120-240 gets multiplied by 2 (200 * 2 = 400)
        $result = $this->frequencyService->transformFrequency(200);
        $this->assertEquals(400, $result);

        // Test frequency below 250 (and not in multiplication ranges) gets set to 250
        $result = $this->frequencyService->transformFrequency(50);
        $this->assertEquals(250, $result); // 50 * 3 = 150, then set to 250

        // Actually test the minimum frequency setting
        $result = $this->frequencyService->transformFrequency(80);
        $this->assertEquals(250, $result); // 80 * 3 = 240, then set to 250
    }

    public function test_generate_harmonics(): void
    {
        $harmonics = $this->frequencyService->generateHarmonics(440.0, 3);

        $this->assertIsArray($harmonics);
        $this->assertCount(5, $harmonics); // subharmonic + fundamental + 3 harmonics

        // Check fundamental frequency
        $fundamental = $harmonics[1];
        $this->assertEquals('Fundamental (F)', $fundamental['name']);
        $this->assertEquals(440.0, $fundamental['value']);
    }

    public function test_frequency_to_note_conversion(): void
    {
        // A4 = 440Hz should return A4
        $note = $this->frequencyService->frequencyToNote(440.0);
        $this->assertStringContainsString('A', $note);

        // Test edge case
        $note = $this->frequencyService->frequencyToNote(0);
        $this->assertEquals('', $note);
    }

    public function test_sanitize_input(): void
    {
        $input = '<script>alert("xss")</script>test topic';
        $sanitized = $this->frequencyService->sanitizeInput($input);

        $this->assertStringNotContainsString('<script>', $sanitized);
        $this->assertStringContainsString('test topic', $sanitized);
    }
}
