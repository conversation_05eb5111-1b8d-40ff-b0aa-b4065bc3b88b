<?php

namespace App\Model\Dashboards;

use Illuminate\Database\Eloquent\Model;

class Dashboard extends Model
{
    protected $guarded = [];
    protected $casts = [
        'dashboard_data' => 'array',
        'forced_users' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo('App\Model\User');
    }

    public function isActive(): bool
    {
        if($this->active == 1){
            return true;
        }

        if($this->active == 0){
            return false;
        }

        $currentUser = auth()->user()->id;

        if(!empty($this->forced_users) && in_array($currentUser, $this->forced_users)){
            return true;
        }

        return false;
    }

    public function getUrl()
    {
        $url = $this->url;
        if (!empty($url)) {
            $url = base64_encode(urlencode($url));
            return route('dashboard.designer', ['encodedUrl' => $url]);
        }

        return route('dashboard.dashboard');
    }
}