<?php

namespace App\Livewire\Dashboard;

use Livewire\Component;
use App\Model\User;
use App\Model\Dashboards\Dashboard;
use App\Traits\LivewireGeneralFunctions;

class UserManager extends Component
{
    use LivewireGeneralFunctions;
    public $dashboard;
    public $dashboardId;
    public $search = '';
    public $assignedUsers = [];
    public $availableUsers = [];
    public $selectedUserId = null;
    public $totalAvailableCount = 0;

    protected $rules = [
        'assignedUsers' => 'array',
    ];

    public function mount($dashboardId)
    {
        $this->dashboardId = $dashboardId;

        try {
            $this->dashboard = Dashboard::find($dashboardId);

            if (!$this->dashboard) {
                $this->showToastr('error', __('Error'), __('Dashboard not found'));
                return;
            }

            // Reset search
            $this->search = '';
            $this->totalAvailableCount = 0;

            // Load currently assigned users
            $this->loadAssignedUsers();

            // Load available users
            $this->searchUsers();

        } catch (\Exception $e) {
            $this->showToastr('error', __('Error'), __('Failed to load dashboard'));
        }
    }

    public function loadAssignedUsers()
    {
        try {
            $forcedUserIds = $this->dashboard->forced_users ?? [];

            if (empty($forcedUserIds)) {
                $this->assignedUsers = [];
                return;
            }

            $this->assignedUsers = User::whereIn('id', $forcedUserIds)
                ->orderBy('first_name')
                ->orderBy('last_name')
                ->get()
                ->map(function ($user) {
                    $name = trim($user->first_name . ' ' . $user->last_name);
                    return [
                        'id' => $user->id,
                        'name' => !empty($name) ? $name : 'User #' . $user->id,
                        'email' => $user->email ?? 'No email',
                    ];
                })
                ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)
                ->values()
                ->toArray();
        } catch (\Exception $e) {
            $this->showToastr('error', __('Error'), __('Failed to load assigned users'));
            $this->assignedUsers = [];
        }
    }

    public function searchUsers()
    {
        $query = User::query();

        // Exclude already assigned users
        $assignedIds = collect($this->assignedUsers)->pluck('id')->toArray();
        $query->whereNotIn('id', $assignedIds);

        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function ($q) {
                $q->where('first_name', 'like', '%' . $this->search . '%')
                    ->orWhere('last_name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ['%' . $this->search . '%']);
            });
        }

        // Get total count before limiting
        $this->totalAvailableCount = $query->count();

        $this->availableUsers = $query
            ->limit(10)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'email' => $user->email,
                ];
            })
            ->toArray();
    }

    public function assignUser($userId)
    {
        try {
            $userId = (int) $userId;
            $user = User::find($userId);

            if (!$user) {
                $this->showToastr('error', __('Error'), __('User not found'));
                return;
            }

            $name = trim($user->first_name . ' ' . $user->last_name);

            // Add to assigned users
            $this->assignedUsers[] = [
                'id' => $user->id,
                'name' => !empty($name) ? $name : 'User #' . $user->id,
                'email' => $user->email ?? 'No email',
            ];

            // Sort assigned users alphabetically
            $this->assignedUsers = collect($this->assignedUsers)
                ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)
                ->values()
                ->toArray();

            // Remove from available users
            $this->availableUsers = collect($this->availableUsers)
                ->reject(function ($u) use ($userId) {
                    return (int) $u['id'] === $userId;
                })
                ->values()
                ->toArray();

            $this->saveForcedUsers();

        } catch (\Exception $e) {
            $this->showToastr('error', __('Error'), __('Failed to assign user: ' . $e->getMessage()));
        }
    }

    public function confirmRemoveUser($userId)
    {
        $user = collect($this->assignedUsers)->firstWhere('id', $userId);

        if ($user) {
            $this->dispatch('swal:confirm', [
                'type' => 'warning',
                'title' => __('Remove User?'),
                'text' => __('Are you sure you want to remove :name from this dashboard?', ['name' => $user['name']]),
                'confirmButtonText' => __('Yes, remove'),
                'cancelButtonText' => __('Cancel'),
                'method' => 'unassignUser',
                'params' => $userId
            ]);
        }
    }

    public function unassignUser($userId)
    {
        try {
            $userId = (int) $userId;

            // Remove from assigned users
            $this->assignedUsers = collect($this->assignedUsers)
                ->reject(function ($user) use ($userId) {
                    return (int) $user['id'] === $userId;
                })
                ->values()
                ->toArray();

            // Save changes first
            $this->saveForcedUsers();

            // Then refresh search to include unassigned user
            $this->searchUsers();

        } catch (\Exception $e) {
            $this->showToastr('error', __('Error'), __('Failed to remove user: ' . $e->getMessage()));
            // Reload users in case of error
            $this->loadAssignedUsers();
            $this->searchUsers();
        }
    }

    public function saveForcedUsers()
    {
        try {
            // Get fresh instance of dashboard
            $this->dashboard = Dashboard::find($this->dashboardId);

            if (!$this->dashboard) {
                $this->showToastr('error', __('Error'), __('Dashboard not found'));
                return;
            }

            $userIds = collect($this->assignedUsers)->pluck('id')->map(function($id) {
                return (int) $id;
            })->toArray();

            // Ensure forced_users is an array
            $this->dashboard->forced_users = $userIds;

            // Force save the model
            $saved = $this->dashboard->save();

            if ($saved) {
                $this->showToastr('success', __('Success'), __('Users updated successfully'));
            } else {
                $this->showToastr('warning', __('Warning'), __('No changes were saved'));
            }

        } catch (\Exception $e) {
            $this->showToastr('error', __('Error'), __('Failed to update users: ' . $e->getMessage()));
            // Reload the dashboard
            $this->dashboard = Dashboard::find($this->dashboardId);
            $this->loadAssignedUsers();
        }
    }

    public function refresh()
    {
        $this->dashboard = Dashboard::find($this->dashboardId);
        $this->loadAssignedUsers();
        $this->searchUsers();
    }

    private function sortAssignedUsers()
    {
        $this->assignedUsers = collect($this->assignedUsers)
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)
            ->values()
            ->toArray();
    }

    public function hydrate()
    {
        // Ensure dashboard is loaded on each request
        if (!$this->dashboard && $this->dashboardId) {
            $this->dashboard = Dashboard::find($this->dashboardId);
        }
    }

    public function dehydrate()
    {
        // Ensure variables are properly set before rendering
        $this->totalAvailableCount = $this->totalAvailableCount ?: 0;
    }

    public function clearSearch()
    {
        $this->search = '';
        $this->searchUsers();
    }

    public function updatedSearch($value)
    {
        $this->searchUsers();
    }

    public function handleSwalConfirmed($data)
    {
        if (isset($data['method']) && $data['method'] === 'unassignUser' && isset($data['params'])) {
            $this->unassignUser($data['params']);
        }
    }

    public function render()
    {
        // Ensure assigned users are always sorted
        $sortedAssignedUsers = collect($this->assignedUsers)
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE)
            ->values()
            ->toArray();

        return view('livewire.dashboard.user-manager', [
            'assignedUsers' => $sortedAssignedUsers,
            'availableUsers' => $this->availableUsers,
            'totalAvailableCount' => $this->totalAvailableCount,
        ]);
    }
}