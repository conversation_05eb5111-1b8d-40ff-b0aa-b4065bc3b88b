<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Contracts\View\View;
use Illuminate\Validation\ValidationException;

/**
 * FrequencyGenerator Livewire Component
 *
 * Handles frequency generation based on topic text input,
 * harmonic calculations, and cart integration.
 */
class FrequencyGenerator extends Component
{
    use LivewireGeneralFunctions;

    /**
     * @var FrequencyService Frequency calculation service
     */
    protected FrequencyService $frequencyService;

    /**
     * @var int Pool identifier
     */
    public int $poolId;

    /**
     * @var array Widget configuration
     */
    public array $widget = [];

    /**
     * @var object|null User details from backend
     */
    public ?object $userDetails = null;

    /**
     * @var object|null Biorhythm configuration details
     */
    public ?object $biorythDetails = null;

    /**
     * @var int Random time value within configured bounds
     */
    public int $randomTime = 30;

    /**
     * @var int Calculated frequency based on topic
     */
    public int $calculatedFrequency = 0;

    /**
     * @var string Topic text input
     */
    public string $topicText = '';

    /**
     * @var string Frequency in Hz
     */
    public string $frequencyHz = '';

    /**
     * @var string Time in seconds
     */
    public string $frequencyTime = '';

    /**
     * @var array Validation rules
     */
    protected $rules = [
        'topicText' => 'required|string|min:1|max:500',
        'frequencyHz' => 'required|numeric|min:250|max:20000',
        'frequencyTime' => 'required|numeric|min:5|max:3600'
    ];

    /**
     * Constructor - inject FrequencyService
     *
     * @param FrequencyService $frequencyService
     */
    public function __construct(FrequencyService $frequencyService)
    {
        $this->frequencyService = $frequencyService;
    }

    /**
     * Component mount lifecycle
     *
     * @param int $poolId Pool identifier
     * @param array $widget Widget configuration
     * @return void
     */
    public function mount(int $poolId, array $widget = []): void
    {
        $this->poolId = $poolId;
        $this->widget = $widget;

        try {
            $this->initializeComponentData();
        } catch (\Exception $e) {
            Log::error('FrequencyGenerator mount error', [
                'error' => $e->getMessage(),
                'poolId' => $poolId,
                'widget' => $widget
            ]);

            $this->setDefaultValues();
        }
    }

    /**
     * Initialize component data from backend services
     *
     * @return void
     * @throws \Exception
     */
    protected function initializeComponentData(): void
    {
        // Cache user details for performance
        $cacheKey = 'user_details_' . auth()->id();
        $this->userDetails = Cache::remember($cacheKey, 300, function () {
            return getUserDetails();
        });

        if (!$this->userDetails) {
            throw new \Exception('User details not found');
        }

        // Get biorhythm details
        $this->biorythDetails = biorythVisibleDetails();

        if (!$this->biorythDetails) {
            throw new \Exception('Biorhythm details not found');
        }

        // Generate random time within bounds using FrequencyService
        $this->randomTime = $this->frequencyService->generateRandomTime($this->biorythDetails);

        // Get topic and calculate frequency
        $topicText = $this->frequencyService->sanitizeInput($this->userDetails->thema_speichern ?? '');

        if (!empty($topicText)) {
            $this->calculatedFrequency = $this->frequencyService->calculateFrequencySafely($topicText);
        }

        // Set initial values
        $this->topicText = $topicText;
        $this->frequencyHz = $this->calculatedFrequency > 0 ? (string)$this->calculatedFrequency : '';
        $this->frequencyTime = (string)$this->randomTime;
    }

    /**
     * Set default values when initialization fails
     *
     * @return void
     */
    protected function setDefaultValues(): void
    {
        $this->userDetails = null;
        $this->biorythDetails = null;
        $this->randomTime = 30;
        $this->calculatedFrequency = 0;
        $this->topicText = '';
        $this->frequencyHz = '';
        $this->frequencyTime = '30';
    }

    /**
     * Handle property updates with rate limiting
     *
     * @param string $propertyName
     * @return void
     */
    public function updated(string $propertyName): void
    {
        // Clear validation errors
        $this->resetErrorBag($propertyName);

        // Handle topic text updates with rate limiting
        if ($propertyName === 'topicText' && !empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    /**
     * Update frequency based on topic text with rate limiting
     *
     * @return void
     */
    protected function updateFrequencyFromTopic(): void
    {
        $userId = auth()->id();

        $result = $this->frequencyService->updateFrequencyFromTopic(
            $this->topicText,
            $userId,
            function($frequency) {
                $this->frequencyHz = (string)$frequency;
            },
            function($result) {
                if ($result['rate_limited']) {
                    $this->showToastr('warning', trans('action.frequency_generator'), $result['message']);
                }
            }
        );
    }



    /**
     * Add frequency to cart with transaction handling
     *
     * @return void
     */
    public function addToCart(): void
    {
        try {
            // Validate inputs
            $this->validate();

            // Additional business logic validation
            $this->validateBusinessRules();

            // Rate limit cart additions (5 per minute)
            $key = 'cart_add_' . auth()->id();
            if (RateLimiter::tooManyAttempts($key, 5)) {
                $seconds = RateLimiter::availableIn($key);
                throw new \Exception(trans('action.too_many_cart_additions', ['seconds' => $seconds]));
            }

            RateLimiter::hit($key, 60);

            // Prepare cart data
            $cartData = $this->prepareCartData();

            // Make request with timeout and retry
            $response = Http::timeout(10)
                ->retry(2, 100)
                ->asForm()
                ->post(url('/Sajax/add2Cart'), $cartData);

            $this->handleCartResponse($response);

        } catch (ValidationException $e) {
            // Validation errors are automatically handled by Livewire
            return;
        } catch (\Exception $e) {
            Log::error('FrequencyGenerator addToCart error', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'data' => [
                    'topic' => substr($this->topicText, 0, 50) . '...',
                    'frequency' => $this->frequencyHz,
                    'time' => $this->frequencyTime
                ]
            ]);

            $this->showToastr('error', trans('action.frequency_generator'),
                $e->getMessage() ?: trans('action.error_occurred'));
        }
    }

    /**
     * Validate business rules beyond basic validation
     *
     * @throws \Exception
     */
    protected function validateBusinessRules(): void
    {
        $validation = $this->frequencyService->validateBusinessRules(
            $this->topicText,
            (int)$this->frequencyHz,
            (int)$this->frequencyTime,
            $this->biorythDetails
        );

        if (!$validation['valid']) {
            throw new \Exception(implode(', ', $validation['errors']));
        }
    }

    /**
     * Prepare cart data for submission
     *
     * @return array
     */
    protected function prepareCartData(): array
    {
        return [
            'ana_id' => 1,
            'name' => $this->frequencyService->sanitizeInput($this->topicText),
            'submenu_id' => '',
            'proID' => '',
            'calculation' => '',
            'male' => '',
            'heart' => '',
            'price' => '',
            'causes_id' => '',
            'medium_id' => '',
            'tipp_id' => '',
            'color' => '',
            'type' => 'Topic',
            'frequency' => (int)$this->frequencyHz,
            'time' => (int)$this->frequencyTime,
            '_token' => csrf_token()
        ];
    }

    /**
     * Handle cart API response
     *
     * @param \Illuminate\Http\Client\Response $response
     * @throws \Exception
     */
    protected function handleCartResponse($response): void
    {
        if (!$response->successful()) {
            throw new \Exception('Cart service unavailable');
        }

        $data = $response->json();

        if ($data['success'] ?? false) {
            // Clear form and reset with new values
            $this->resetFormWithNewValues();

            // Show success notification
            $this->showToastr('success', trans('action.frequency_generator'),
                trans('action.topic_cart_save'));

            // Update cart UI
            $this->dispatch('cartUpdated');

            // Optional: Show cart
            // $this->showCart();
        } else {
            $message = $data['message'] ?? trans('action.error_occurred');

            // Check for specific error types
            if (isset($data['error_code'])) {
                switch ($data['error_code']) {
                    case 'cart_full':
                        $message = trans('action.cart_max_allow_alert');
                        break;
                    case 'invalid_frequency':
                        $message = trans('action.invalid_frequency');
                        break;
                }
            }

            throw new \Exception($message);
        }
    }

    /**
     * Reset form with new random values
     *
     * @return void
     */
    protected function resetFormWithNewValues(): void
    {
        $this->reset(['topicText', 'frequencyHz']);

        // Generate new random time using FrequencyService
        $this->randomTime = $this->frequencyService->generateRandomTime($this->biorythDetails);
        $this->frequencyTime = (string)$this->randomTime;
    }

    /**
     * Calculate harmonics for the current frequency
     *
     * @return void
     */
    public function calculateHarmonics(): void
    {
        if (empty($this->frequencyHz)) {
            $this->addError('frequencyHz', trans('action.frequency_placeholder'));
            return;
        }

        try {
            $frequency = (float)$this->frequencyHz;

            // Validate frequency range using FrequencyService
            if (!$this->frequencyService->validateFrequency((int)$frequency)) {
                $this->addError('frequencyHz', trans('action.invalid_frequency'));
                return;
            }

            // Calculate harmonics using FrequencyService
            $harmonics = $this->frequencyService->generateHarmonics($frequency);

            // Generate HTML table for modal using FrequencyService
            $harmonicsHtml = $this->frequencyService->renderHarmonicsTable($harmonics);

            // Show in modal
            $this->showModal($harmonicsHtml, trans('action.calculate_harmonics'));

            // Log harmonic calculation for analytics
            Log::info('Harmonics calculated', [
                'frequency' => $frequency,
                'user_id' => auth()->id()
            ]);

        } catch (\Exception $e) {
            Log::error('FrequencyGenerator calculateHarmonics error', [
                'error' => $e->getMessage(),
                'frequency' => $this->frequencyHz
            ]);

            $this->showToastr('error', trans('action.frequency_generator'),
                trans('action.error_occurred'));
        }
    }







    /**
     * Get custom validation messages
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'topicText.required' => trans('action.note_couldnt_save'),
            'topicText.min' => trans('action.topic_too_short'),
            'topicText.max' => trans('action.topic_too_long'),
            'frequencyHz.required' => trans('action.frequency_time_both_required'),
            'frequencyHz.numeric' => trans('action.frequency_must_be_number'),
            'frequencyHz.min' => trans('action.invalid_frequency'),
            'frequencyHz.max' => trans('action.invalid_frequency'),
            'frequencyTime.required' => trans('action.frequency_time_both_required'),
            'frequencyTime.numeric' => trans('action.time_must_be_number'),
            'frequencyTime.min' => trans('action.invalid_time'),
            'frequencyTime.max' => trans('action.invalid_time'),
        ];
    }

    /**
     * Render the component
     *
     * @return View
     */
    public function render(): View
    {
        return view('livewire.dashboard.widgets.navigation.frequency-generator');
    }
}