<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use App\Traits\LivewireGeneralFunctions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Contracts\View\View;
use Illuminate\Validation\ValidationException;

/**
 * FrequencyGenerator Livewire Component
 *
 * Handles frequency generation based on topic text input,
 * harmonic calculations, and cart integration.
 */
class FrequencyGenerator extends Component
{
    use LivewireGeneralFunctions;

    /**
     * @var int Pool identifier
     */
    public int $poolId;

    /**
     * @var array Widget configuration
     */
    public array $widget = [];

    /**
     * @var object|null User details from backend
     */
    public ?object $userDetails = null;

    /**
     * @var object|null Biorhythm configuration details
     */
    public ?object $biorythDetails = null;

    /**
     * @var int Random time value within configured bounds
     */
    public int $randomTime = 30;

    /**
     * @var int Calculated frequency based on topic
     */
    public int $calculatedFrequency = 0;

    /**
     * @var string Topic text input
     */
    public string $topicText = '';

    /**
     * @var string Frequency in Hz
     */
    public string $frequencyHz = '';

    /**
     * @var string Time in seconds
     */
    public string $frequencyTime = '';

    /**
     * @var array Validation rules
     */
    protected $rules = [
        'topicText' => 'required|string|min:1|max:500',
        'frequencyHz' => 'required|numeric|min:250|max:20000',
        'frequencyTime' => 'required|numeric|min:5|max:3600'
    ];

    /**
     * Component mount lifecycle
     *
     * @param int $poolId Pool identifier
     * @param array $widget Widget configuration
     * @return void
     */
    public function mount(int $poolId, array $widget = []): void
    {
        $this->poolId = $poolId;
        $this->widget = $widget;

        try {
            $this->initializeComponentData();
        } catch (\Exception $e) {
            Log::error('FrequencyGenerator mount error', [
                'error' => $e->getMessage(),
                'poolId' => $poolId,
                'widget' => $widget
            ]);

            $this->setDefaultValues();
        }
    }

    /**
     * Initialize component data from backend services
     *
     * @return void
     * @throws \Exception
     */
    protected function initializeComponentData(): void
    {
        // Cache user details for performance
        $cacheKey = 'user_details_' . auth()->id();
        $this->userDetails = Cache::remember($cacheKey, 300, function () {
            return getUserDetails();
        });

        if (!$this->userDetails) {
            throw new \Exception('User details not found');
        }

        // Get biorhythm details
        $this->biorythDetails = biorythVisibleDetails();

        if (!$this->biorythDetails) {
            throw new \Exception('Biorhythm details not found');
        }

        // Generate random time within bounds
        $minPrice = max(5, (int)($this->biorythDetails->gs_min_price ?? 5));
        $maxPrice = min(3600, (int)($this->biorythDetails->gs_max_price ?? 3600));
        $this->randomTime = rand($minPrice, $maxPrice);

        // Get topic and calculate frequency
        $topicText = $this->sanitizeInput($this->userDetails->thema_speichern ?? '');

        if (!empty($topicText)) {
            $this->calculatedFrequency = $this->calculateFrequencySafely($topicText);
        }

        // Set initial values
        $this->topicText = $topicText;
        $this->frequencyHz = $this->calculatedFrequency > 0 ? (string)$this->calculatedFrequency : '';
        $this->frequencyTime = (string)$this->randomTime;
    }

    /**
     * Set default values when initialization fails
     *
     * @return void
     */
    protected function setDefaultValues(): void
    {
        $this->userDetails = null;
        $this->biorythDetails = null;
        $this->randomTime = 30;
        $this->calculatedFrequency = 0;
        $this->topicText = '';
        $this->frequencyHz = '';
        $this->frequencyTime = '30';
    }

    /**
     * Handle property updates with rate limiting
     *
     * @param string $propertyName
     * @return void
     */
    public function updated(string $propertyName): void
    {
        // Clear validation errors
        $this->resetErrorBag($propertyName);

        // Handle topic text updates with rate limiting
        if ($propertyName === 'topicText' && !empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }
    }

    /**
     * Update frequency based on topic text with rate limiting
     *
     * @return void
     */
    protected function updateFrequencyFromTopic(): void
    {
        $key = 'frequency_calc_' . auth()->id();

        // Rate limit frequency calculations (10 per minute)
        if (RateLimiter::tooManyAttempts($key, 10)) {
            $seconds = RateLimiter::availableIn($key);
            $this->showToastr('warning', trans('action.frequency_generator'),
                trans('action.too_many_calculations', ['seconds' => $seconds]));
            return;
        }

        RateLimiter::hit($key, 60);

        try {
            $sanitizedTopic = $this->sanitizeInput($this->topicText);
            $newFrequency = $this->calculateFrequencySafely($sanitizedTopic);

            if ($newFrequency > 0) {
                $this->frequencyHz = (string)$newFrequency;
            }
        } catch (\Exception $e) {
            Log::warning('Frequency calculation error', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
        }
    }

    /**
     * Calculate frequency safely with error handling
     *
     * @param string $topic
     * @return int
     */
    protected function calculateFrequencySafely(string $topic): int
    {
        try {
            // Cache calculation results
            $cacheKey = 'freq_calc_' . md5($topic);

            return Cache::remember($cacheKey, 3600, function () use ($topic) {
                $frequency = calculationFrequency($topic);

                // Validate frequency is within acceptable range
                if ($frequency < 250 || $frequency > 20000) {
                    return 0;
                }

                return (int)$frequency;
            });
        } catch (\Exception $e) {
            Log::error('Frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($topic, 0, 50) . '...'
            ]);
            return 0;
        }
    }

    /**
     * Add frequency to cart with transaction handling
     *
     * @return void
     */
    public function addToCart(): void
    {
        try {
            // Validate inputs
            $this->validate();

            // Additional business logic validation
            $this->validateBusinessRules();

            // Rate limit cart additions (5 per minute)
            $key = 'cart_add_' . auth()->id();
            if (RateLimiter::tooManyAttempts($key, 5)) {
                $seconds = RateLimiter::availableIn($key);
                throw new \Exception(trans('action.too_many_cart_additions', ['seconds' => $seconds]));
            }

            RateLimiter::hit($key, 60);

            // Prepare cart data
            $cartData = $this->prepareCartData();

            // Make request with timeout and retry
            $response = Http::timeout(10)
                ->retry(2, 100)
                ->asForm()
                ->post(url('/Sajax/add2Cart'), $cartData);

            $this->handleCartResponse($response);

        } catch (ValidationException $e) {
            // Validation errors are automatically handled by Livewire
            return;
        } catch (\Exception $e) {
            Log::error('FrequencyGenerator addToCart error', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'data' => [
                    'topic' => substr($this->topicText, 0, 50) . '...',
                    'frequency' => $this->frequencyHz,
                    'time' => $this->frequencyTime
                ]
            ]);

            $this->showToastr('error', trans('action.frequency_generator'),
                $e->getMessage() ?: trans('action.error_occurred'));
        }
    }

    /**
     * Validate business rules beyond basic validation
     *
     * @throws \Exception
     */
    protected function validateBusinessRules(): void
    {
        // Check if frequency matches topic (prevent tampering)
        if (!empty($this->topicText)) {
            $expectedFrequency = $this->calculateFrequencySafely($this->topicText);
            $actualFrequency = (int)$this->frequencyHz;

            // Allow some tolerance for manual adjustments
            if ($expectedFrequency > 0 && abs($expectedFrequency - $actualFrequency) > 100) {
                Log::warning('Frequency mismatch detected', [
                    'expected' => $expectedFrequency,
                    'actual' => $actualFrequency,
                    'user_id' => auth()->id()
                ]);
            }
        }

        // Check time bounds from biorhythm settings
        if ($this->biorythDetails) {
            $minTime = (int)($this->biorythDetails->gs_min_price ?? 5);
            $maxTime = (int)($this->biorythDetails->gs_max_price ?? 3600);
            $time = (int)$this->frequencyTime;

            if ($time < $minTime || $time > $maxTime) {
                throw new \Exception(trans('action.invalid_time_range', [
                    'min' => $minTime,
                    'max' => $maxTime
                ]));
            }
        }
    }

    /**
     * Prepare cart data for submission
     *
     * @return array
     */
    protected function prepareCartData(): array
    {
        return [
            'ana_id' => 1,
            'name' => $this->sanitizeInput($this->topicText),
            'submenu_id' => '',
            'proID' => '',
            'calculation' => '',
            'male' => '',
            'heart' => '',
            'price' => '',
            'causes_id' => '',
            'medium_id' => '',
            'tipp_id' => '',
            'color' => '',
            'type' => 'Topic',
            'frequency' => (int)$this->frequencyHz,
            'time' => (int)$this->frequencyTime,
            '_token' => csrf_token()
        ];
    }

    /**
     * Handle cart API response
     *
     * @param \Illuminate\Http\Client\Response $response
     * @throws \Exception
     */
    protected function handleCartResponse($response): void
    {
        if (!$response->successful()) {
            throw new \Exception('Cart service unavailable');
        }

        $data = $response->json();

        if ($data['success'] ?? false) {
            // Clear form and reset with new values
            $this->resetFormWithNewValues();

            // Show success notification
            $this->showToastr('success', trans('action.frequency_generator'),
                trans('action.topic_cart_save'));

            // Update cart UI
            $this->dispatch('cartUpdated');

            // Optional: Show cart
            // $this->showCart();
        } else {
            $message = $data['message'] ?? trans('action.error_occurred');

            // Check for specific error types
            if (isset($data['error_code'])) {
                switch ($data['error_code']) {
                    case 'cart_full':
                        $message = trans('action.cart_max_allow_alert');
                        break;
                    case 'invalid_frequency':
                        $message = trans('action.invalid_frequency');
                        break;
                }
            }

            throw new \Exception($message);
        }
    }

    /**
     * Reset form with new random values
     *
     * @return void
     */
    protected function resetFormWithNewValues(): void
    {
        $this->reset(['topicText', 'frequencyHz']);

        // Generate new random time
        if ($this->biorythDetails) {
            $minTime = (int)($this->biorythDetails->gs_min_price ?? 5);
            $maxTime = (int)($this->biorythDetails->gs_max_price ?? 3600);
            $this->randomTime = rand($minTime, $maxTime);
            $this->frequencyTime = (string)$this->randomTime;
        } else {
            $this->frequencyTime = '30';
        }
    }

    /**
     * Calculate harmonics for the current frequency
     *
     * @return void
     */
    public function calculateHarmonics(): void
    {
        if (empty($this->frequencyHz)) {
            $this->addError('frequencyHz', trans('action.frequency_placeholder'));
            return;
        }

        try {
            $frequency = (float)$this->frequencyHz;

            // Validate frequency range
            if ($frequency < 250 || $frequency > 20000) {
                $this->addError('frequencyHz', trans('action.invalid_frequency'));
                return;
            }

            // Calculate harmonics with proper formatting
            $harmonics = $this->generateHarmonics($frequency);

            // Generate HTML table for modal
            $harmonicsHtml = $this->renderHarmonicsTable($harmonics);

            // Show in modal
            $this->showModal($harmonicsHtml, trans('action.calculate_harmonics'));

            // Log harmonic calculation for analytics
            Log::info('Harmonics calculated', [
                'frequency' => $frequency,
                'user_id' => auth()->id()
            ]);

        } catch (\Exception $e) {
            Log::error('FrequencyGenerator calculateHarmonics error', [
                'error' => $e->getMessage(),
                'frequency' => $this->frequencyHz
            ]);

            $this->showToastr('error', trans('action.frequency_generator'),
                trans('action.error_occurred'));
        }
    }

    /**
     * Generate harmonic frequencies
     *
     * @param float $fundamental
     * @return array
     */
    protected function generateHarmonics(float $fundamental): array
    {
        return [
            [
                'name' => 'Subharmonic (F/2)',
                'value' => $fundamental / 2,
                'note' => $this->frequencyToNote($fundamental / 2)
            ],
            [
                'name' => 'Fundamental (F)',
                'value' => $fundamental,
                'note' => $this->frequencyToNote($fundamental)
            ],
            [
                'name' => '2nd Harmonic (2F)',
                'value' => $fundamental * 2,
                'note' => $this->frequencyToNote($fundamental * 2)
            ],
            [
                'name' => '3rd Harmonic (3F)',
                'value' => $fundamental * 3,
                'note' => $this->frequencyToNote($fundamental * 3)
            ],
            [
                'name' => '4th Harmonic (4F)',
                'value' => $fundamental * 4,
                'note' => $this->frequencyToNote($fundamental * 4)
            ],
            [
                'name' => '5th Harmonic (5F)',
                'value' => $fundamental * 5,
                'note' => $this->frequencyToNote($fundamental * 5)
            ]
        ];
    }

    /**
     * Convert frequency to musical note (optional feature)
     *
     * @param float $frequency
     * @return string
     */
    protected function frequencyToNote(float $frequency): string
    {
        if ($frequency <= 0) return '';

        // A4 = 440Hz reference
        $a4 = 440;
        $c0 = $a4 * pow(2, -4.75);

        $notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

        if ($frequency > $c0) {
            $halfSteps = 12 * log($frequency / $c0) / log(2);
            $octave = floor($halfSteps / 12);
            $noteIndex = round($halfSteps) % 12;

            return $notes[$noteIndex] . $octave;
        }

        return '';
    }

    /**
     * Render harmonics table HTML
     *
     * @param array $harmonics
     * @return string
     */
    protected function renderHarmonicsTable(array $harmonics): string
    {
        $rows = '';
        foreach ($harmonics as $harmonic) {
            $frequency = number_format($harmonic['value'], 2);
            $note = $harmonic['note'] ? " ({$harmonic['note']})" : '';
            $rows .= "<tr>
                <td>{$harmonic['name']}</td>
                <td>{$frequency} Hz{$note}</td>
            </tr>";
        }

        return '
            <div class="harmonics-result">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>' . trans('action.harmonic') . '</th>
                            <th>' . trans('action.frequency') . ' (Hz)</th>
                        </tr>
                    </thead>
                    <tbody>' . $rows . '</tbody>
                </table>
                <div class="mt-3 text-muted small">
                    <i class="fas fa-info-circle"></i> 
                    ' . trans('action.harmonics_info') . '
                </div>
            </div>';
    }

    /**
     * Sanitize input to prevent XSS
     *
     * @param string $input
     * @return string
     */
    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Get custom validation messages
     *
     * @return array
     */
    protected function messages(): array
    {
        return [
            'topicText.required' => trans('action.note_couldnt_save'),
            'topicText.min' => trans('action.topic_too_short'),
            'topicText.max' => trans('action.topic_too_long'),
            'frequencyHz.required' => trans('action.frequency_time_both_required'),
            'frequencyHz.numeric' => trans('action.frequency_must_be_number'),
            'frequencyHz.min' => trans('action.invalid_frequency'),
            'frequencyHz.max' => trans('action.invalid_frequency'),
            'frequencyTime.required' => trans('action.frequency_time_both_required'),
            'frequencyTime.numeric' => trans('action.time_must_be_number'),
            'frequencyTime.min' => trans('action.invalid_time'),
            'frequencyTime.max' => trans('action.invalid_time'),
        ];
    }

    /**
     * Render the component
     *
     * @return View
     */
    public function render(): View
    {
        return view('livewire.dashboard.widgets.navigation.frequency-generator');
    }
}