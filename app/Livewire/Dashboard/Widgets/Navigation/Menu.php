<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class Menu extends Component
{
    public $data;
    public $addUserStatus;
    public $auth;
    public $subusers;
    public $farbklang = false;

    public function mount($data = [])
    {
        $this->data = $data;
        $this->getMenuInfo();
    }

    private function getMenuInfo()
    {
        $userDetails = getUserDetails();
        $this->addUserStatus = (maxAddUser() <= 0) ? false : true;
        $this->auth = Auth::user();
        $this->subusers = getSubUser();
        $data['filterid'] = !isset($data['filterid']) ? $userDetails?->userfilter()?->first(['filter_type'])?->filter_type : $data['filterid'];    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.menu');
    }
}
