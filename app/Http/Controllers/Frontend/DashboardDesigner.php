<?php

namespace App\Http\Controllers\Frontend;

use App\Model\Pool;
use Illuminate\Http\Request;
use App\Traits\HandlesImageUpload;
use App\Model\Dashboards\Dashboard;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Traits\PoolAnalysesGeneralFunctions;

class DashboardDesigner extends Controller
{
    use PoolAnalysesGeneralFunctions, HandlesImageUpload;

    public function __construct()
    {
        //custom auth logic
        $this->middleware(function ($request, $next) {
            if (!auth()->check()) {
                return redirect()->route('login');
            }

            if (!checkIfUserAllowedToAccessDashboardDesigner()) {
                return redirect()->route('dashboard.dashboard')->with('error', 'You do not have permission to access this page.');
            }

            return $next($request);
        });
    }
    
    public function index(Request $request)
    {
        $encodedUrl = $request->url;
        $decodedUrl = base64_decode(urldecode($encodedUrl));
        $path = parse_url($decodedUrl, PHP_URL_PATH);
        return view('Frontend.dashboard.designer.index', ['path' => $path,]);
    }

    public function editWidget(Request $request, $dashboardId, $widgetId)
    {
        $dashboard = Dashboard::findOrFail($dashboardId);
        $widget = collect($dashboard->dashboard_data ?? [])->firstWhere('id', $widgetId);
        if (!$widget) {
            abort(404, 'Widget not found.');
        }
        $sortingDiagram = $widget['settings']['sorting'] ?? [];
        $poolId = $widget['pool_type'] ?? null;
        $pool = Pool::with(['analyses' => fn($q) => $q->orderBy('id')])
                    ->find($poolId);
        $analyses = $this->processDiagramAnalyses($sortingDiagram, $pool);
        $pools = Pool::select('id', 'pool_name')->orderBy('pool_name')->get();

        return view('Frontend.dashboard.designer.edit-widget', [
            'widget' => $widget,
            'analyses' => $analyses,
            'widgetId' => $widgetId,
            'dashboardId' => $dashboardId,
            'pools' => $pools,
        ]);
    }

    public function saveWidget(Request $request, $dashboardId, $widgetId)
    {
        // Validate the request
        $request->validate([
            'widget_title' => 'required|string|max:255',
            'pool_type' => 'required|exists:pools,id',
            'image_position' => 'nullable|in:left,right',
            'chakra_image' => 'nullable|image|mimes:jpg,jpeg,png|max:200',
        ]);

        $sorting = json_decode($request->sorted_ids, true);
        $dashboard = Dashboard::findOrFail($dashboardId);
        $widgets = $dashboard->dashboard_data ?? [];
        $imagePosition = $request->image_position ?? 'right';
        $widgetTitle = $request->widget_title;
        $poolType = $request->pool_type;

        $imagePath = null;
        if ($request->hasFile('chakra_image')) {
            $imagePath = $this->handleImageUpload($request, 'chakra_image', 'exist_chakra_image', 'chakraImages');
        }

        $updatedWidgets = collect($widgets)->map(function ($item) use ($widgetId, $sorting, $imagePath, $imagePosition, $widgetTitle, $poolType) {
            if ($item['id'] === $widgetId) {
                $item['title'] = $widgetTitle;
                $item['pool_type'] = $poolType;
                $item['settings']['sorting'] = $sorting;
                if ($imagePath) {
                    $item['settings']['image'] = $imagePath;
                }
                $item['settings']['image_position'] = $imagePosition;
            }
            return $item;
        })->all();

        $dashboard->dashboard_data = $updatedWidgets;
        $dashboard->save();
 
        return redirect()->route('dashboard.designer.edit', [
            'dashboardId' => $dashboardId,
            'widgetId' => $widgetId,
        ])->with('success', 'Widget updated successfully.');
    }

    private function processDiagramAnalyses(array $OldsortedDiagram, Pool $pool): array
    {
        if (blank($OldsortedDiagram)) {
            return $pool->analyses->pluck('name', 'id')->toArray();
        }
    
        $ids = array_map('intval', $OldsortedDiagram);
        $relation = $pool->analyses();
        $table = $relation->getRelated()->getTable();
    
        return $relation
            ->select("$table.id", "$table.name")
            ->whereIn("$table.id", $ids)
            ->orderByRaw('FIELD(' . $table . '.id, ' . implode(',', $ids) . ')')
            ->pluck("$table.name", "$table.id")
            ->toArray();
    }

    public function users(Request $request, $dashboardId)
    {
        return view('Frontend.dashboard.designer.users', [
            'dashboardId' => $dashboardId,
        ]);
    }
}
