<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Calculation\FrequencyService;
use App\Services\Calculation\CalculationService;

class FrequencyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(CalculationService::class, function ($app) {
            return new CalculationService();
        });

        $this->app->singleton(FrequencyService::class, function ($app) {
            return new FrequencyService($app->make(CalculationService::class));
        });

        // Register alias for easier access
        $this->app->alias(FrequencyService::class, 'frequency.service');
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
