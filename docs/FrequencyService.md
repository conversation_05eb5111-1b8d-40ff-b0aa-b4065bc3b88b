# FrequencyService Documentation

## Overview

The `FrequencyService` is a centralized service for handling all frequency-related calculations across dashboard widgets and components. It provides a unified interface for frequency calculations, caching, rate limiting, validation, and business logic transformations.

## Features

- **Centralized Logic**: All frequency calculation logic is now in one place
- **Caching**: Automatic caching of calculation results to improve performance
- **Rate Limiting**: Built-in rate limiting for user interactions
- **Validation**: Comprehensive validation of frequency ranges and business rules
- **Error Handling**: Robust error handling with logging
- **Harmonics**: Musical harmonic calculations and note conversion
- **Transformations**: Business rule transformations for frequency values

## Installation

The service is automatically registered via the `FrequencyServiceProvider` in `config/app.php`.

## Usage

### Basic Usage

```php
use App\Services\Calculation\FrequencyService;

// Inject via constructor
public function __construct(FrequencyService $frequencyService)
{
    $this->frequencyService = $frequencyService;
}

// Calculate frequency
$frequency = $this->frequencyService->calculateFrequency('some topic text');
```

### Safe Calculation with <PERSON><PERSON><PERSON> Handling

```php
$frequency = $this->frequencyService->calculateFrequencySafely('topic text', [
    'apply_transformations' => true,
    'fallback_frequency' => 250,
    'cache_ttl' => 3600
]);
```

### Rate Limited Calculation

```php
$result = $this->frequencyService->calculateFrequencyWithRateLimit(
    'topic text',
    auth()->id()
);

if ($result['success']) {
    $frequency = $result['frequency'];
} else {
    // Handle rate limiting
    $retryAfter = $result['retry_after'];
}
```

### Real-time Updates with Callbacks

```php
$this->frequencyService->updateFrequencyFromTopic(
    $this->topicText,
    auth()->id(),
    function($frequency) {
        // Success callback
        $this->frequencyHz = (string)$frequency;
    },
    function($result) {
        // Error callback
        if ($result['rate_limited']) {
            $this->showToastr('warning', $result['message']);
        }
    }
);
```

## API Reference

### Core Methods

#### `calculateFrequency(string $topic, array $options = []): int`

Calculate frequency for a given topic with full configuration options.

**Options:**
- `cache_ttl` (int): Cache time-to-live in seconds (default: 3600)
- `min_frequency` (int): Minimum allowed frequency (default: 250)
- `max_frequency` (int): Maximum allowed frequency (default: 20000)
- `apply_transformations` (bool): Apply business rule transformations (default: true)
- `log_errors` (bool): Log errors (default: true)

#### `calculateFrequencySafely(string $topic, array $options = []): int`

Safe calculation with comprehensive error handling and fallback values.

**Additional Options:**
- `fallback_frequency` (int): Frequency to return on error (default: 0)

#### `calculateFrequencyWithRateLimit(string $topic, int $userId, array $options = []): array`

Calculate frequency with rate limiting protection.

**Returns:**
```php
[
    'success' => bool,
    'frequency' => int,
    'rate_limited' => bool,
    'retry_after' => int,
    'message' => string
]
```

### Validation Methods

#### `validateFrequency(int $frequency, int $minFreq = 250, int $maxFreq = 20000): bool`

Validate if frequency is within acceptable range.

#### `validateBusinessRules(string $topic, int $frequency, int $time, ?object $biorythDetails = null): array`

Comprehensive business rule validation.

**Returns:**
```php
[
    'valid' => bool,
    'errors' => array
]
```

### Harmonic Methods

#### `generateHarmonics(float $fundamental, int $harmonicCount = 5): array`

Generate harmonic frequencies for a fundamental frequency.

#### `frequencyToNote(float $frequency): string`

Convert frequency to musical note representation.

#### `renderHarmonicsTable(array $harmonics): string`

Render harmonics as HTML table for modal display.

### Utility Methods

#### `generateRandomTime(?object $biorythDetails = null): int`

Generate random time within biorhythm bounds.

#### `sanitizeInput(string $input): string`

Sanitize input to prevent XSS and normalize text.

#### `transformFrequency(int $frequency): int`

Apply business rule transformations to frequency values.

## Migration Guide

### From FrequencyGenerator Widget

**Before:**
```php
protected function calculateFrequencySafely(string $topic): int
{
    // Custom implementation with caching and validation
}

protected function updateFrequencyFromTopic(): void
{
    // Custom rate limiting and calculation logic
}
```

**After:**
```php
public function __construct(FrequencyService $frequencyService)
{
    $this->frequencyService = $frequencyService;
}

protected function updateFrequencyFromTopic(): void
{
    $result = $this->frequencyService->updateFrequencyFromTopic(
        $this->topicText,
        auth()->id(),
        function($frequency) {
            $this->frequencyHz = (string)$frequency;
        }
    );
}
```

### From WonTopic Widget

**Before:**
```php
protected function calculateFrequencySafely(string $topic): int
{
    // Custom transformation logic
    $frequency = calculationFrequency(trim($topic));
    if ($frequency > 0 && $frequency < 120) {
        $frequency *= 3;
    }
    // ... more transformations
}
```

**After:**
```php
$frequency = $this->frequencyService->calculateFrequencySafely($topic, [
    'apply_transformations' => true,
    'fallback_frequency' => 250
]);
```

## Configuration

The service uses the following default configurations:

- **Cache TTL**: 3600 seconds (1 hour)
- **Rate Limit**: 10 calculations per minute per user
- **Frequency Range**: 250-20000 Hz
- **Transformations**: Applied by default

These can be overridden via the options parameter in method calls.

## Error Handling

The service provides comprehensive error handling:

1. **Calculation Errors**: Logged and fallback values returned
2. **Rate Limiting**: Graceful handling with retry information
3. **Validation Errors**: Clear error messages for business rules
4. **Cache Failures**: Automatic fallback to direct calculation

## Performance

- **Caching**: Results are cached to reduce computation
- **Rate Limiting**: Prevents abuse and reduces server load
- **Lazy Loading**: Service is only instantiated when needed
- **Optimized Calculations**: Reuses existing helper functions

## Testing

The service can be easily mocked for testing:

```php
$mockService = Mockery::mock(FrequencyService::class);
$mockService->shouldReceive('calculateFrequency')
    ->with('test topic')
    ->andReturn(440);

$this->app->instance(FrequencyService::class, $mockService);
```

## Future Enhancements

- **Cache Tagging**: For more sophisticated cache management
- **Async Calculations**: For heavy computational loads
- **Analytics**: Track calculation patterns and performance
- **API Versioning**: Support for different calculation algorithms
