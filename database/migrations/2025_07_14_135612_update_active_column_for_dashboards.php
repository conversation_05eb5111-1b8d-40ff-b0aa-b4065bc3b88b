<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dashboards', function (Blueprint $table) {
            // Change 'active' from boolean to tinyInteger
            // This will allow 0, 1, and 2 states
            $table->tinyInteger('active')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dashboards', function (Blueprint $table) {
            // Revert 'active' back to boolean if needed, adjust default accordingly
            $table->boolean('active')->default(false)->change();
        });
    }
};