<div class="container-fluid">
    {{-- Header Section --}}
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="h2 mb-2">{{ __('User Access Management') }}</h1>
                    <p class="text-muted mb-0">{{ __('Control which users can access this dashboard when it\'s inactive') }}</p>
                </div>
                @if($dashboard)
                    <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                        <span class="badge {{ $dashboard->active ? 'bg-success' : 'bg-secondary' }} p-2">
                            <i class="fas fa-circle me-1" style="font-size: 8px;"></i>
                            {{ $dashboard->active ? __('Dashboard Active') : __('Dashboard Inactive') }}
                        </span>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @if(!$dashboard)
        {{-- Error State --}}
        <div class="alert alert-danger d-flex align-items-center" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <div>{{ __('Dashboard not found') }}</div>
        </div>
    @else
        {{-- Main Content Grid --}}
        <div class="row">
            {{-- Search & Available Users Panel --}}
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>
                            {{ __('Available Users') }}
                        </h5>
                    </div>

                    {{-- Search Bar --}}
                    <div class="card-body bg-light border-bottom">
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text"
                                   wire:model.debounce.300ms="search"
                                   placeholder="{{ __('Search by name or email...') }}"
                                   class="form-control border-start-0 ps-0"
                                   autocomplete="off">
                            @if(!empty($search))
                                <button wire:click="clearSearch"
                                        wire:loading.attr="disabled"
                                        class="btn btn-outline-secondary"
                                        type="button"
                                        title="{{ __('Clear search') }}">
                                    <i class="fas fa-times"></i>
                                </button>
                            @endif
                            <button wire:click="searchUsers"
                                    wire:loading.attr="disabled"
                                    class="btn btn-primary"
                                    type="button"
                                    style="min-width: 80px;">
                                <span  wire:target="searchUsers">{{ __('Search') }}</span>
                            </button>
                        </div>
                        @if(!empty($search) && $totalAvailableCount > 10)
                            <small class="text-muted d-block mt-2">{{ __('Showing first 10 of :total results. Try a more specific search.', ['total' => $totalAvailableCount]) }}</small>
                        @endif
                        @if(!empty($search))
                            <small class="text-muted d-block mt-1">
                                {{ __('Searching for:') }} "{{ $search }}" -
                                {{ __('Found:') }} {{ $totalAvailableCount }} {{ __('users') }}
                            </small>
                        @endif
                    </div>

                    {{-- Available Users List --}}
                    <div class="card-body p-3 overflow-auto" style="max-height: 400px;">
                        <div wire:loading.class="opacity-50" wire:target="search, searchUsers">
                            @if(count($availableUsers) > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($availableUsers as $user)
                                        <div wire:key="available-{{ $user['id'] }}" class="list-group-item border-0 mb-2 bg-light rounded">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center overflow-hidden me-2">
                                                    <div class="avatar-circle bg-primary text-white me-3">
                                                        {{ strtoupper(substr($user['name'], 0, 1)) }}
                                                    </div>
                                                    <div class="user-info">
                                                        <h6 class="mb-0">{{ $user['name'] }}</h6>
                                                        <small class="text-muted">{{ $user['email'] }}</small>
                                                    </div>
                                                </div>
                                                <button wire:click="assignUser({{ $user['id'] }})"
                                                        wire:loading.attr="disabled"
                                                        class="btn btn-sm btn-primary"
                                                        style="min-width: 70px;">
                                                    <span  wire:target="assignUser({{ $user['id'] }})">
                                                        {{ __('Add') }}
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted mb-0">
                                        @if(empty($search))
                                            {{ __('All users are already assigned') }}
                                        @else
                                            {{ __('No users found matching') }} "<strong>{{ $search }}</strong>"
                                        @endif
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            {{-- Assigned Users Panel --}}
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-check me-2"></i>
                            {{ __('Assigned Users') }}
                        </h5>
                        <span class="badge bg-white text-success">
                            {{ count($assignedUsers) }} {{ __('users') }}
                        </span>
                    </div>

                    {{-- Assigned Users List --}}
                    <div class="card-body p-3 overflow-auto" style="max-height: 500px;">
                        @if(count($assignedUsers) > 0)
                            <div class="list-group list-group-flush">
                                @foreach($assignedUsers as $user)
                                    <div wire:key="assigned-{{ $user['id'] }}" class="list-group-item border-0 mb-2 rounded" style="background-color: #e8f5e9;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center overflow-hidden me-2">
                                                <div class="avatar-circle bg-success text-white me-3">
                                                    {{ strtoupper(substr($user['name'], 0, 1)) }}
                                                </div>
                                                <div class="user-info">
                                                    <h6 class="mb-0">{{ $user['name'] }}</h6>
                                                    <small class="text-muted">{{ $user['email'] }}</small>
                                                </div>
                                            </div>
                                            <button wire:click="unassignUser({{ $user['id'] }})"
                                                    wire:loading.attr="disabled"
                                                    class="btn btn-sm btn-danger"
                                                    style="min-width: 80px;">
                                                <span  wire:target="unassignUser({{ $user['id'] }})">
                                                    {{ __('Remove') }}
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{{ __('No users have been assigned yet') }}</p>
                                <small class="text-muted">{{ __('Search and add users from the left panel') }}</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

@section('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
        flex-shrink: 0;
        margin-inline-end: 7px;
    }

    .list-group-item {
        transition: all 0.3s ease;
        padding: 12px 16px;
    }

    .list-group-item:hover {
        transform: translateX(5px);
        box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
    }

    .btn {
        transition: all 0.2s ease;
    }

    .btn:hover:not(:disabled) {
        transform: scale(1.05);
    }

    .btn:disabled {
        cursor: not-allowed;
        opacity: 0.65;
    }

    .opacity-50 {
        opacity: 0.5;
    }

    .card {
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .card-header {
        border-bottom: 0;
        font-weight: 600;
        padding: 1rem 1.25rem;
    }

    .user-info {
        min-width: 0;
        flex: 1;
    }

    .user-info h6 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .user-info small {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: block;
    }

    .spinner-border-sm {
        vertical-align: middle;
    }

    @media (max-width: 991.98px) {
        .text-lg-end {
            text-align: left !important;
        }

        .list-group-item:hover {
            transform: none;
        }
    }
</style>
@endsection