<div>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">
    <div class="navigation-buttons">
        @if(($farbklang && $auth->user_type == \App\Enums\UserRoll::Therapist) || !$farbklang)
        <div class="livewire-menu-container" style="position: static; display: block;">
            <a class="dropdown-item livewire-menu-item @if($farbklang) dashActive_btn @endif" data-url='{{ url('/') }}'
            href="javascript:void(0)" data-name="{{trans('action.system_setting')}}" data-tab="1" id="livewire-menu-1">
            <i class="ion ion-md-settings ">&nbsp;</i><span>{{trans('action.system_setting')}}</span>
            </a>
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.last_treatment')}}" data-tab="3" id="livewire-menu-3">
            <i class="ion ion-md-cart">&nbsp;</i><span>{{trans('action.last_treatment')}}</span>
            </a>
        
            @if($auth->user_type == \App\Enums\UserRoll::Therapist || $auth->user_type == \App\Enums\UserRoll::Staff)
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.pdf_heading')}}" data-tab="2" id="livewire-menu-2"><i
                class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.pdf_heading')}}</span></a>
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.digitalcon&focusPDF')}}" data-tab="5" id="livewire-menu-5"><i
                class="fas fa-file-pdf">&nbsp;</i><span>{{trans('action.digitalcon&focusPDF')}}</span></a>
            @endif
            @if ($auth->user_type == \App\Enums\UserRoll::Admin)
            <a class="dropdown-item livewire-menu-item" href="javascript:void(0)" data-url='{{ url('/') }}'
            data-name="{{trans('action.user')}}" data-tab="4" id="livewire-menu-4"><i
                class="ion ion-ios-contacts">&nbsp;</i><span>{{trans('action.user')}}</span></a>
            @endif
        </div>
        @endif
        @if($farbklang && $auth->user_type == \App\Enums\UserRoll::Admin)
        <button type="button" class="tabs-itemBox-Style" id="users_button"><i
                class="fas fa-users">&nbsp;</i><span>{{trans('action.user')}}</span></button>
        @endif
    </div>
    <div class="tab-contents">
        <div id="tab-1" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body web-kit-scroll">
                    <ul class="sys-setting">
                        <li>
                            <p>
                                {{trans('action.calculation_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-1 mr-2">{{trans('action.year')}}</div>
                                    <label class="switcher switcher-lg switcher-success m-0">
                                        <input type="checkbox" onclick="changeYearMonth()" id="changeYearMonth"
                                            @if($userDetails->calculation_with == 1) {{'checked'}} @endif
                                        class="switcher-input">
                                        <span class="switcher-indicator">
                                            <span class="switcher-yes"></span>
                                            <span class="switcher-no"></span>
                                        </span>
                                    </label>
                                    <div class="flex-shrink-1 text-success ml-2">{{trans('action.month')}}</div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.date_system_dashboard')}}
                            </p>
    
                            <div class="right-side">
                                <div class="right-side ml-3 ml-sm-0">
                                    <input type="text" class="form-control datef" data-date="{{ $userDetails->datumcore }}"
                                        id="datePicker" onchange="dateChange()" placeholder="mm/dd/yyyy"
                                        value="{{ \Carbon\Carbon::parse($userDetails->datumcore)->toDateString() }}">
                                </div>
                            </div>
                        </li>
                        <li>
                            <p>
                                {{trans('action.sorting_system_dashboard')}}
                            </p>
                            <div class="right-side">
                                <select class="custom-select" id="changeShowFilter" onchange="changeShowFilter()">
                                    <option @if ($data['filterid']==1) {{ 'selected' }} @endif value="1">A-Z</option>
                                    <option @if ($data['filterid']==2) {{ 'selected' }} @endif value="2">Z-A</option>
                                    <option @if ($data['filterid']==3) {{ 'selected' }} @endif value="3">1-100</option>
                                    <option @if ($data['filterid']==4) {{ 'selected' }} @endif value="4">100-1</option>
                                </select>
                            </div>
                        </li>
                    </ul>
    
                </div>
            </div>
        </div>
    
        <div id="tab-2" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="show_normal_pdfs">
    
                </div>
            </div>
        </div>
    
        <div id="tab-3" class="tab-content hide">
            <div class="card">
                <div class="arrow"></div>
                <div class="card-body" id="save_treatment_cart_list" style="padding: 15px 10px 15px 15px;">
                </div>
            </div>
        </div>
    
        <div id="tab-4" class="tab-content hide">
            @if ($auth->user_type == \App\Enums\UserRoll::Admin)
            <div class="card">
                <div class="@if($farbklang) arrow arrow3 @else arrow @endif"></div>
                <div class="card-body">
                    <div class="row">
                        {{-- user --}}
                        <div class="col-md-12">
                            <ul class="users-rankwise">
                                <li>
                                    <div class="user-single-box text-center">
                                        <div class="usb-photo">
                                            @if($userDetails->id !== $auth->id)
                                            <img data-name="{{$auth->fullName}}" data-email="{{$auth->email}}"
                                                data-view="true" src="{{ getProfileImage($auth) }}" id="upimg">
                                            @else
                                            <img data-name="{{$userDetails->fullName}}" data-email="{{$userDetails->email}}"
                                                data-view="true" data-profileimage="true" id="upimg">
                                            @endif
                                        </div>
                                        <div class="usb-content">
                                            <p>{{ $admin->fullName}}</p>
                                            <span class="text-primary">{{trans('action.no_access2')}}</span>
                                        </div>
                                    </div>
                                </li>
    
                                @if($subusers != null)
                                @foreach ($subusers as $user)
                                <li>
                                    <div class="user-single-box text-center">
                                        <div class="usb-photo" onclick="switch_user({{$user->id}})">
                                            {{-- @if($user->photo == "")
                                            <img class="rounded-circle m-1 p-1"
                                                data-src="{!! ($user->gender == 2)? asset('/images/female.jpg') : asset('/images/avatar.png') !!}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                            @else
                                            <img class="rounded-circle m-1 p-1"
                                                data-src="{{ getEmbededImage('/profile/users/'.$user->photo) }}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                            @endif --}}
                                            <img class="rounded-circle m-1 p-1" data-src="{{getProfileImage($user)}}"
                                                alt="{{ucfirst(substr($user->first_name,0,1)).' '.ucfirst(substr($user->last_name,0,1)) }}" />
                                        </div>
                                        <button type="button" class="minus-btn" onclick="deleteSubUser({{$user->id}})"
                                            data-toggle="tooltip" data-placement="bottom" data-state="secondary"
                                            title="{{ trans('action.delete')}} {{$user->first_name}}&nbsp;{{ $user->last_name }}"><i
                                                class="fa text-danger fa-minus-circle"></i></button>
    
                                        <div class="usb-content">
                                            <p>{{$user->fullName }}</p>
                                        </div>
                                    </div>
                                </li>
                                @endforeach
                                @endif
    
                                {{-- creat user --}}
                                @if($addUserStatus)
                                <li>
                                    <div class="user-single-box text-center">
                                        <a href="{{ route('users.show') }}">
                                            <div class="usb-icon">
                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                            </div>
                                        </a>
                                        <div class="usb-content">
                                            <p>{{trans('action.create_new_user')}}</p>
                                        </div>
                                    </div>
                                </li>
                                @endif
                            </ul>
                        </div>
    
                    </div>
                </div>
            </div>
            @endif
        </div>
    
        <div id="tab-5" class="tab-content hide">
            @if ($auth->user_type == \App\Enums\UserRoll::Therapist || $auth->user_type == \App\Enums\UserRoll::Staff)
            <div class="card ">
                <div class="arrow"></div>
                <div class="card-body" id="dc_pdf_view_show">
    
                </div>
            </div>
            @endif
        </div>
    </div>
    <script>
        // Livewire menu functionality with unique classes
        function initializeLivewireMenu() {
            // Open tab 1 by default
            $('.tab-content').hide();
            $('#tab-1').removeClass('hide').show();

            $('.livewire-menu-item').off('click').on('click', function () {
            var changename = $(this).data('name');
            var dropdown = $(this).data('tab');
            var url = $(this).data('url');
            
            if(dropdown == 1 || dropdown == 4){
                $('.tab-content').hide();
                $('#tab-' + dropdown).removeClass('hide');
                $('#tab-' + dropdown).show();
            }else{
                $('.tab-content').hide();
                $('#tab-' + dropdown).removeClass('hide').show();
                if($('#tab-' + dropdown + ' .card .card-body ul li').length == 0){
                $('#tab-' + dropdown + ' .card .card-body').html(`<div class="preloader"><img style="top:20px !important" src="/images/Fill-4.png" alt=""></div>`).attr('style','min-height:270px');
                $.ajax({
                    type: 'GET',
                    url: url + '/Sajax/get_data/' + dropdown,
                    dataType: "json",
                    success: function(res){
                    $('#tab-' + dropdown + ' .card .card-body').removeAttr('style').empty();
                    if(dropdown == 2){
                        $('#show_normal_pdfs').empty();
                        $('#show_normal_pdfs').append(res.pdf_list);
                    }
                    else if(dropdown == 3){
                        $('#save_treatment_cart_list').empty();
                        $('#save_treatment_cart_list').append(res.treatment_list);
                    }
                    else if(dropdown == 5){
                        $('#dc_pdf_view_show').empty();
                        $('#dc_pdf_view_show').append(res.dcpdf_list);
                    }
                    },
                    error: function(data){
                    console.log(data);
                    }
                });
                }
            }
            });
        }
        // Initialize on page load
        $(document).ready(function() {
            initializeLivewireMenu();
        });
    </script>
    {{-- <script src="{{ asset('js/dashboard/right-panel.js') }}"></script> --}}
</div>
