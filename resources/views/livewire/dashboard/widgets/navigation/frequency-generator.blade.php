{{-- resources/views/livewire/dashboard/widgets/navigation/frequency-generator.blade.php --}}
<div>
    <style>
        @keyframes glow {
            0% {
                box-shadow: 0 0 5px rgba(2, 160, 101, 0.5), 0 0 10px rgba(2, 160, 101, 0.3);
            }

            50% {
                box-shadow: 0 0 20px rgba(2, 160, 101, 0.8), 0 0 30px rgba(2, 160, 101, 0.5);
            }

            100% {
                box-shadow: 0 0 5px rgba(2, 160, 101, 0.5), 0 0 10px rgba(2, 160, 101, 0.3);
            }
        }

        @keyframes glowText {
            0% {
                text-shadow: 0 0 5px rgba(2, 160, 101, 0.5);
                opacity: 0.7;
            }

            50% {
                text-shadow: 0 0 20px rgba(2, 160, 101, 0.8);
                opacity: 1;
            }

            100% {
                text-shadow: 0 0 5px rgba(2, 160, 101, 0.5);
                opacity: 0.7;
            }
        }

        @keyframes glowButton {
            0% {
                box-shadow: 0 0 5px rgba(255, 193, 7, 0.5), inset 0 0 5px rgba(255, 193, 7, 0.2);
            }

            50% {
                box-shadow: 0 0 20px rgba(255, 193, 7, 0.8), inset 0 0 10px rgba(255, 193, 7, 0.3);
            }

            100% {
                box-shadow: 0 0 5px rgba(255, 193, 7, 0.5), inset 0 0 5px rgba(255, 193, 7, 0.2);
            }
        }

        @keyframes glowPrimary {
            0% {
                box-shadow: 0 0 5px rgba(13, 110, 253, 0.5), inset 0 0 5px rgba(13, 110, 253, 0.2);
            }

            50% {
                box-shadow: 0 0 20px rgba(13, 110, 253, 0.8), inset 0 0 10px rgba(13, 110, 253, 0.3);
            }

            100% {
                box-shadow: 0 0 5px rgba(13, 110, 253, 0.5), inset 0 0 5px rgba(13, 110, 253, 0.2);
            }
        }

        .glowing-input {
            animation: glow 1.5s ease-in-out infinite;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .glowing-text {
            animation: glowText 1.5s ease-in-out infinite;
        }

        .glowing-button {
            animation: glowButton 1.5s ease-in-out infinite;
        }

        .btn-primary.glowing-button {
            animation: glowPrimary 1.5s ease-in-out infinite;
        }

        .position-relative {
            position: relative;
        }

        .position-absolute {
            z-index: 10;
        }
    </style>

    <div class="tab-navigation font-awesome mb-4">
        {{-- <div class="navigation-buttons">
            <button type="button" id="nav-widget-frequency-generator-button-{{ $widget['id'] ?? 'default' }}"
                class="tabs-itemBox-Style">
                <i class="fas fa-wave-square">&nbsp;</i>
                <span>{{ trans('action.frequency_generator') }}</span>
            </button>
        </div> --}}

        <div class="tab-contents">
            <div class="card frequencyGeneratorTab fade show"
                id="nav-widget-frequencyGeneratorTab-{{ $widget['id'] ?? 'default' }}"
                data-livewire-component="{{ $this->getId() }}">
                <div class="card-body">
                    <form wire:submit.prevent="addToCart">
                        <div class="form-group topic">
                            <div class="position-relative">
                                <textarea rows="2" class="form-control" wire:model.live.debounce.500ms="topicText"
                                    wire:loading.class="glowing-input" wire:target="topicText"
                                    placeholder="{{ trans('action.topic_name_placeholder') }}"></textarea>
                                <div wire:loading wire:target="topicText" class="position-absolute top-0 end-0 p-2">
                                    <small class="text-muted glowing-text">
                                        <i class="fas fa-calculator"></i>
                                    </small>
                                </div>
                            </div>
                            @error('topicText')
                            <small class="text-danger">{{ $message }}</small>
                            @enderror
                        </div>
                        <div class="frequency-time-controls">
                            <div class="frequency-input-group">
                                <div class="input-group">
                                    <input type="number" class="form-control" style="border: 0 !important;" min="250"
                                        max="20000" step="50" placeholder="{{ trans('action.frequency_placeholder') }}"
                                        wire:model.defer="frequencyHz" wire:loading.class="glowing-input"
                                        wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                                    <span class="input-group-text" wire:loading.class="glowing-text"
                                        wire:target="topicText">
                                        {{ trans('action.unit_hertz') }}
                                    </span>
                                    <button type="button" class="btn btn-warning harmonics-btn"
                                        wire:click="calculateHarmonics" wire:loading.attr="disabled"
                                        wire:loading.class="glowing-button" wire:target="calculateHarmonics"
                                        title="{{ __('action.calculate_harmonics') }}" data-bs-toggle="tooltip"
                                        data-bs-placement="top">
                                        <i class="fas fa-wave-square"></i>
                                    </button>
                                </div>
                                @error('frequencyHz')
                                <small class="form-text text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                            <div class="time-input-group">
                                <div class="input-group">
                                    <input type="number" class="form-control" style="border: 0 !important;" min="5"
                                        max="3600" step="1" placeholder="{{ trans('action.seconds_placeholder') }}"
                                        wire:model.defer="frequencyTime">
                                    <span class="input-group-text">{{ trans('action.unit_seconds') }}</span>
                                </div>
                                @error('frequencyTime')
                                <small class="form-text text-danger">{{ $message }}</small>
                                @enderror
                            </div>
                        </div>
                    </form>
                    <div class="text-center frequency-btns">
                        <button type="button" class="btn btn-primary icon" wire:click="addToCart"
                            wire:loading.attr="disabled" wire:loading.class="glowing-button" wire:target="addToCart">
                            <span>{{ trans('action.cart') }}</span>
                            <span wire:loading wire:target="addToCart" class="glowing-text">{{
                                trans('action.processing') }}...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>