@switch($diagramType)
    @case('menu')
        <livewire:dashboard.widgets.navigation.menu
            :poolId="$widget['pool_type']"
            :widget="$widget"
            :wire:key="'widget-menu-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()"
        />
    @break
    @case('won_topic')
        <livewire:dashboard.widgets.navigation.won-topic 
            :poolId="$widget['pool_type']"
            :widget="$widget"
            :wire:key="'widget-won-topic-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()"
        />
    @break
    @case('frequency_generator')
        <livewire:dashboard.widgets.navigation.frequency-generator
            :poolId="$widget['pool_type']"
            :widget="$widget"
            :wire:key="'widget-frequency-generator-' . $widget['id'] . '-pool-' . $widget['pool_type'] . '-' . rand()"
        />
    @break
@endswitch 